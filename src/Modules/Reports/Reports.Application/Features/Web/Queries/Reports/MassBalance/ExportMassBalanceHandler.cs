using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Http;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Orion.SharedKernel.Application.Common.Mapping;
using Orion.SharedKernel.Application.Exceptions;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web;
using Reports.Domain;
using Reports.Domain.Common;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;
using Shared.Application.Common.Services;
using Shared.Domain.Constants;

namespace Reports.Application.Features.Web.Queries.Reports.MassBalance;

/// <summary>
/// Handler for exporting Mass Balance reports to Excel format
/// </summary>
internal class ExportMassBalanceHandler : MappingService, IRequestHandler<ExportMassBalanceRequest, ExportMassBalanceResponse>
{
    private readonly IReportsUnitOfWork _unitOfWork;
    private readonly IFileExporterService _fileExporterService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private const int MinutesTillCacheInvalidation = 360;
    private const string DefaultExcelDateFormat = "yyyy/MM/dd HH:mm:ss";
    private const string MassBalanceFilePrefix = "Balance_de_Masas_";

    public ExportMassBalanceHandler(
        IMapper mapper,
        IReportsUnitOfWork unitOfWork,
        IFileExporterService fileExporterService,
        IHttpContextAccessor httpContextAccessor) : base(mapper)
    {
        _unitOfWork = unitOfWork;
        _fileExporterService = fileExporterService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<ExportMassBalanceResponse> Handle(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        // Get the Mass Balance data using the same logic as GetMassBalanceHandler
        var massBalanceData = await GetMassBalanceData(request, cancellationToken);
        
        // Transform to export DTO
        var exportData = TransformToExportDto(massBalanceData, request);
        
        // Generate Excel file
        var excelBytes = GenerateMassBalanceExcel(exportData);
        
        // Determine format and create response
        var format = GetExcelFormat(request.ExcelFormat);
        var contentType = GetContentType(format);
        var fileExtension = format.ToString().ToLower();
        var fileName = GenerateFileName(request.FromDate, fileExtension);
        
        var result = Results.File(
            fileContents: excelBytes,
            contentType: contentType,
            fileDownloadName: fileName);

        return new ExportMassBalanceResponse
        {
            TotalRecords = exportData.BalanceSheetRows.Count + exportData.DistributionRows.Count,
            Result = result
        };
    }

    private async Task<Domain.ValueObjects.MassBalance> GetMassBalanceData(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        // Reuse the same data retrieval logic from GetMassBalanceHandler
        var weighinDistributions = await GetDistributions(request, cancellationToken);
        var f14 = (IReadOnlyList<ReportFormat14>)await GetReportFormat14(request, cancellationToken);
        var f34 = (IReadOnlyList<ReportFormat34>)await GetReportFormat34(request, cancellationToken);
        var weighins = (IReadOnlyList<WeighingScale>)await GetWeighins(request, cancellationToken);
        var recyclingAreas = (IReadOnlyList<RecyclingArea>)await GetRecyclingAreas(cancellationToken);

        return Domain.ValueObjects.MassBalance.Create()
            .ProcessDistributions(weighinDistributions)
            .ProcessWeighins(weighins)
            .ProcessRecollectionAndTransport(f14)
            .ProcessFinalDisposition(f34, recyclingAreas)
            .Validate();
    }

    private MassBalanceExportDto TransformToExportDto(Domain.ValueObjects.MassBalance massBalance, ExportMassBalanceRequest request)
    {
        var period = $"{request.FromDate:yyyy/MM}";
        
        return new MassBalanceExportDto
        {
            Title = "Balance de Masas",
            GenerationDate = DateTime.Now,
            Period = period,
            BalanceSheetRows = CreateBalanceSheetRows(massBalance),
            FinalDispositionSummary = CreateFinalDispositionSummary(massBalance),
            DistributionRows = CreateDistributionRows(massBalance)
        };
    }

    private List<BalanceSheetRowDto> CreateBalanceSheetRows(Domain.ValueObjects.MassBalance massBalance)
    {
        var rows = new List<BalanceSheetRowDto>();
        
        // Combine F14 (RecollectionAndTransport) and F34 (FinalDisposition) data by area
        var recollectionAreas = massBalance.RecollectionAndTransport.PerArea.ToDictionary(x => x.RecyclingArea, x => x.Resume);
        var finalDispositionAreas = massBalance.FinalDisposition.PerArea.ToDictionary(x => x.RecyclingArea, x => x.Resume);
        
        // Get all unique areas
        var allAreas = recollectionAreas.Keys.Union(finalDispositionAreas.Keys).OrderBy(x => x != "Medellín").ThenBy(x => x);
        
        foreach (var area in allAreas)
        {
            var recollection = recollectionAreas.GetValueOrDefault(area);
            var finalDisposition = finalDispositionAreas.GetValueOrDefault(area);
            
            rows.Add(new BalanceSheetRowDto
            {
                AreaCode = GetAreaCode(area),
                AreaName = area,
                UrbanCleaningTons = recollection?.UrbanCleaning ?? 0,
                SweepingTons = recollection?.Sweeping ?? 0,
                NonRecyclableTons = recollection?.NonRecyclable ?? 0,
                RejectionTons = recollection?.Rejection ?? 0,
                RecyclableTons = recollection?.Recyclable ?? 0,
                TotalByNUAP = finalDisposition?.Total ?? 0,
                Discounts = finalDisposition?.Discount ?? 0
            });
        }
        
        return rows;
    }

    private FinalDispositionSummaryDto CreateFinalDispositionSummary(Domain.ValueObjects.MassBalance massBalance)
    {
        return new FinalDispositionSummaryDto
        {
            WeighingEmvariasTons = massBalance.Weighins.Emvarias,
            WeighingTotalTons = massBalance.Weighins.Total,
            FinalDispositionEmvariasTons = massBalance.FinalDisposition.Totals.Emvarias,
            FinalDispositionTotalTons = massBalance.FinalDisposition.Totals.Total,
            FinalDispositionDiscountTons = massBalance.FinalDisposition.Totals.Discount
        };
    }

    private List<DistributionRowDto> CreateDistributionRows(Domain.ValueObjects.MassBalance massBalance)
    {
        return massBalance.Distributions.Select(d => new DistributionRowDto
        {
            RecyclingArea = d.RecyclingArea,
            ReportedTons = d.ReportedTons,
            Trips = d.Trips,
            CalculatedDistributedTons = d.DistributedTons,
            CalculatedTotalTons = d.TotalTons,
            CalculatedDeviationTons = d.DeviationTons,
            TollSharedRouteTons = d.TollSharedRouteTons,
            CalculatedDistributionTollPercentage = d.DistributionTollPercentage,
            Compensation = d.DistributedTons * d.DistributionTollPercentage // Calculate compensation
        }).ToList();
    }

    private string GetAreaCode(string areaName)
    {
        // Map area names to codes - this might need to be retrieved from database
        return areaName switch
        {
            "Medellín" => "440405001",
            "Itaguí" => "440360001",
            "Otros" => "000000000",
            _ => "000000000"
        };
    }

    private byte[] GenerateMassBalanceExcel(MassBalanceExportDto exportData)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet("Balance de Masas");

        var currentRow = 0;
        
        // Create header section
        currentRow = CreateMassBalanceHeader(sheet, currentRow, exportData.Title, exportData.GenerationDate, exportData.Period);
        
        // Create main balance sheet table
        currentRow = CreateBalanceSheetTable(sheet, currentRow, exportData.BalanceSheetRows);
        
        // Create final disposition summary table
        currentRow = CreateFinalDispositionSummary(sheet, currentRow, exportData.FinalDispositionSummary);
        
        // Create distribution table
        currentRow = CreateDistributionTable(sheet, currentRow, exportData.DistributionRows);
        
        // Apply formatting
        ApplyMassBalanceFormatting(sheet);

        // Apply borders to merged regions to fix border display issues
        ApplyBordersToMergedRegions(sheet);

        // Auto-size columns
        for (int i = 0; i < 15; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        // Set specific width for column A (index 0) to ensure timestamp displays properly
        sheet.SetColumnWidth(0, 25 * 256); // 25 characters width for "Generado: 08/09/2025 12:20"

        // Set specific width for column I (index 8) to ensure "Total por NUAP" displays properly
        sheet.SetColumnWidth(8, 18 * 256); // 18 characters width

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    private int CreateMassBalanceHeader(ISheet sheet, int startRow, string title, DateTime generationDate, string period)
    {
        var currentRow = startRow;

        // Title row
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue(title);

        // Merge title across columns and create all cells in the merged range
        var titleMergeRegion = new CellRangeAddress(startRow, startRow, 0, 11);
        sheet.AddMergedRegion(titleMergeRegion);

        // Create all cells in the merged range to ensure proper border application
        for (int col = 1; col <= 11; col++)
        {
            titleRow.CreateCell(col);
        }

        // Generation date row
        var dateRow = sheet.CreateRow(currentRow++);
        var dateCell = dateRow.CreateCell(0);
        dateCell.SetCellValue($"Generado: {generationDate:dd/MM/yyyy HH:mm}");

        // Period row
        var periodRow = sheet.CreateRow(currentRow++);
        var periodCell = periodRow.CreateCell(0);
        periodCell.SetCellValue($"Período: {period}");

        // Empty row for spacing
        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private int CreateBalanceSheetTable(ISheet sheet, int startRow, List<BalanceSheetRowDto> data)
    {
        var currentRow = startRow;

        // Create main header with proper centering
        var mainHeaderRow = sheet.CreateRow(currentRow++);
        var mainHeaderCell = mainHeaderRow.CreateCell(0);
        mainHeaderCell.SetCellValue("Balance de Masas - Datos por Área de Prestación");

        // Merge the main header across all columns (0-11) and create all cells in the merged range
        var mainHeaderMergeRegion = new CellRangeAddress(currentRow - 1, currentRow - 1, 0, 11);
        sheet.AddMergedRegion(mainHeaderMergeRegion);

        // Create all cells in the merged range to ensure proper border application
        for (int col = 1; col <= 11; col++)
        {
            mainHeaderRow.CreateCell(col);
        }

        // Create sub headers
        var subHeaderRow = sheet.CreateRow(currentRow++);

        // Column headers
        var headers = new[]
        {
            "Área de Prestación", "Nombre de Área de Prestación",
            "Toneladas de Limpieza Urbana", "Toneladas de Barrido",
            "Toneladas de Residuos No Aprovechables", "Toneladas de Rechazos de Residuos Aprovechados",
            "Toneladas de Residuos Aprovechables", "Toneladas de Barrido + Toneladas de Residuos No Aprovechables",
            "Total por NUAP", "Descuentos", "Total por NUAP - Descuentos", "Diferencia (F34-F14)"
        };

        for (int i = 0; i < headers.Length; i++)
        {
            var cell = subHeaderRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
        }

        // Create data rows
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);

            dataRow.CreateCell(0).SetCellValue(row.AreaCode);
            dataRow.CreateCell(1).SetCellValue(row.AreaName);
            dataRow.CreateCell(2).SetCellValue((double)row.UrbanCleaningTons);
            dataRow.CreateCell(3).SetCellValue((double)row.SweepingTons);
            dataRow.CreateCell(4).SetCellValue((double)row.NonRecyclableTons);
            dataRow.CreateCell(5).SetCellValue((double)row.RejectionTons);
            dataRow.CreateCell(6).SetCellValue((double)row.RecyclableTons);

            // Formula: Barrido + No Aprovechables
            var totalF14Cell = dataRow.CreateCell(7);
            totalF14Cell.SetCellFormula($"D{currentRow}+E{currentRow}");

            dataRow.CreateCell(8).SetCellValue((double)row.TotalByNUAP);
            dataRow.CreateCell(9).SetCellValue((double)row.Discounts);

            // Formula: Total NUAP - Descuentos
            var totalF34Cell = dataRow.CreateCell(10);
            totalF34Cell.SetCellFormula($"I{currentRow}-J{currentRow}");

            // Formula: Diferencia F34-F14
            var differenceCell = dataRow.CreateCell(11);
            differenceCell.SetCellFormula($"K{currentRow}-H{currentRow}");
        }

        // Create totals row
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(1).SetCellValue("SUMA TOTAL");

        var dataStartRow = startRow + 2;
        var dataEndRow = currentRow - 1;

        for (int col = 2; col <= 11; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            if (col == 7 || col == 10 || col == 11) // Formula columns
            {
                totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
            }
            else
            {
                totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
            }
        }

        // Empty rows for spacing
        sheet.CreateRow(currentRow++);
        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private string GetColumnLetter(int columnIndex)
    {
        var letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        if (columnIndex < 26)
            return letters[columnIndex].ToString();

        return letters[columnIndex / 26 - 1].ToString() + letters[columnIndex % 26].ToString();
    }

    private int CreateFinalDispositionSummary(ISheet sheet, int startRow, FinalDispositionSummaryDto summary)
    {
        var currentRow = startRow;

        // Summary table title with proper centering
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue("Resumen Disposición Final");

        // Merge the title across all summary columns (0-3) and create all cells in the merged range
        var titleMergeRegion = new CellRangeAddress(currentRow - 1, currentRow - 1, 0, 3);
        sheet.AddMergedRegion(titleMergeRegion);

        // Create all cells in the merged range to ensure proper border application
        for (int col = 1; col <= 3; col++)
        {
            titleRow.CreateCell(col);
        }

        // Headers
        var headerRow = sheet.CreateRow(currentRow++);
        headerRow.CreateCell(0).SetCellValue("Concepto");
        headerRow.CreateCell(1).SetCellValue("Emvarias (Ton)");
        headerRow.CreateCell(2).SetCellValue("Total (Ton)");
        headerRow.CreateCell(3).SetCellValue("Descuentos (Ton)");

        // Weighing data row
        var weighingRow = sheet.CreateRow(currentRow++);
        weighingRow.CreateCell(0).SetCellValue("Integración Balanzas");
        weighingRow.CreateCell(1).SetCellValue((double)summary.WeighingEmvariasTons);
        weighingRow.CreateCell(2).SetCellValue((double)summary.WeighingTotalTons);
        weighingRow.CreateCell(3).SetCellValue(0); // No discounts for weighing

        // Final disposition data row
        var finalDispRow = sheet.CreateRow(currentRow++);
        finalDispRow.CreateCell(0).SetCellValue("Disposición Final");
        finalDispRow.CreateCell(1).SetCellValue((double)summary.FinalDispositionEmvariasTons);
        finalDispRow.CreateCell(2).SetCellValue((double)summary.FinalDispositionTotalTons);
        finalDispRow.CreateCell(3).SetCellValue((double)summary.FinalDispositionDiscountTons);

        // Empty rows for spacing
        sheet.CreateRow(currentRow++);
        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private int CreateDistributionTable(ISheet sheet, int startRow, List<DistributionRowDto> data)
    {
        var currentRow = startRow;

        // Distribution table title with proper centering
        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell(0);
        titleCell.SetCellValue("Distribución por Área de Prestación");

        // Merge the title across all distribution columns (0-8) and create all cells in the merged range
        var titleMergeRegion = new CellRangeAddress(currentRow - 1, currentRow - 1, 0, 8);
        sheet.AddMergedRegion(titleMergeRegion);

        // Create all cells in the merged range to ensure proper border application
        for (int col = 1; col <= 8; col++)
        {
            titleRow.CreateCell(col);
        }

        // Headers
        var headerRow = sheet.CreateRow(currentRow++);
        var distributionHeaders = new[]
        {
            "Área de Reciclaje", "Toneladas Reportadas", "Viajes", "Toneladas Distribuidas Calculadas",
            "Toneladas Totales Calculadas", "Toneladas de Desviación Calculadas", "Toneladas Ruta Compartida Peaje",
            "Porcentaje Distribución Peaje Calculado", "Compensación"
        };

        for (int i = 0; i < distributionHeaders.Length; i++)
        {
            headerRow.CreateCell(i).SetCellValue(distributionHeaders[i]);
        }

        // Create data rows
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);

            dataRow.CreateCell(0).SetCellValue(row.RecyclingArea);
            dataRow.CreateCell(1).SetCellValue((double)row.ReportedTons);
            dataRow.CreateCell(2).SetCellValue(row.Trips);
            dataRow.CreateCell(3).SetCellValue((double)row.CalculatedDistributedTons);
            dataRow.CreateCell(4).SetCellValue((double)row.CalculatedTotalTons);
            dataRow.CreateCell(5).SetCellValue((double)row.CalculatedDeviationTons);
            dataRow.CreateCell(6).SetCellValue((double)row.TollSharedRouteTons);
            dataRow.CreateCell(7).SetCellValue((double)row.CalculatedDistributionTollPercentage);

            // Formula for compensation: Distributed Tons * Percentage
            var compensationCell = dataRow.CreateCell(8);
            compensationCell.SetCellFormula($"D{currentRow}*H{currentRow}");
        }

        // Create totals row
        var totalsRow = sheet.CreateRow(currentRow++);
        totalsRow.CreateCell(0).SetCellValue("TOTAL");

        var distDataStartRow = startRow + 2;
        var distDataEndRow = currentRow - 1;

        // Sum formulas for all numeric columns including Viajes
        for (int col = 1; col <= 8; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{distDataStartRow}:{GetColumnLetter(col)}{distDataEndRow})");
        }

        return currentRow;
    }

    private void ApplyMassBalanceFormatting(ISheet sheet)
    {
        var workbook = sheet.Workbook;

        // Create styles
        var titleStyle = CreateTitleStyle(workbook);
        var headerStyle = CreateHeaderStyle(workbook);
        var dataStyle = CreateDataStyle(workbook);
        var integerStyle = CreateIntegerStyle(workbook);
        var calculatedStyle = CreateCalculatedStyle(workbook);
        var compensationStyle = CreateCompensationStyle(workbook);
        var totalStyle = CreateTotalStyle(workbook);

        // Apply title formatting (first 3 rows)
        for (int row = 0; row < 3; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = 0; col < 12; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        cell.CellStyle = titleStyle;
                    }
                }
            }
        }

        // Apply header formatting to all header rows
        ApplyHeaderFormatting(sheet, headerStyle);

        // Apply data formatting
        ApplyDataFormatting(sheet, dataStyle, integerStyle, calculatedStyle, compensationStyle);

        // Apply total row formatting
        ApplyTotalFormatting(sheet, totalStyle);

        // Apply specialized border formatting to distribution table
        ApplyDistributionTableBorders(sheet, workbook);
    }

    private ICellStyle CreateTitleStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.FontHeightInPoints = 16;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        return style;
    }

    private ICellStyle CreateHeaderStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private ICellStyle CreateDataStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000");
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private ICellStyle CreateIntegerStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0");
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private ICellStyle CreateDistributionTableBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000");

        // Apply thick borders for outer perimeter and totals row
        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private ICellStyle CreateDistributionTableIntegerBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0");

        // Apply thick borders for outer perimeter and totals row
        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private ICellStyle CreateDistributionTableCompensationBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        // Explicit format for positive and negative numbers: positive;negative;zero
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000;-#,##0.000;0.000");
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;

        // Apply thick borders for outer perimeter and totals row
        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private ICellStyle CreateCalculatedStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000");
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private ICellStyle CreateCompensationStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        // Explicit format for positive and negative numbers: positive;negative;zero
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000;-#,##0.000;0.000");
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private ICellStyle CreateTotalStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat("#,##0.000");
        style.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private void ApplyHeaderFormatting(ISheet sheet, ICellStyle headerStyle)
    {
        // Apply to balance sheet headers (rows 5-6 only, NOT row 7)
        // Row 7 should be treated as data, not header
        for (int row = 5; row <= 6; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = 0; col < 12; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        cell.CellStyle = headerStyle;
                    }
                }
            }
        }

        // Apply to all section headers - find title and header rows
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0);
                if (firstCell != null)
                {
                    var cellValue = firstCell.StringCellValue;
                    // Balance sheet main header
                    if (cellValue.Contains("Balance de Masas - Datos por Área"))
                    {
                        for (int col = 0; col < 12; col++) // Balance sheet has 12 columns
                        {
                            var cell = row.GetCell(col);
                            if (cell != null)
                            {
                                cell.CellStyle = headerStyle;
                            }
                        }
                    }
                    // Summary section title and header rows
                    else if (cellValue == "Resumen Disposición Final" || cellValue == "Concepto")
                    {
                        for (int col = 0; col < 4; col++) // Summary table has 4 columns
                        {
                            var cell = row.GetCell(col);
                            if (cell != null)
                            {
                                cell.CellStyle = headerStyle;
                            }
                        }
                    }
                    // Distribution section title and header rows
                    else if (cellValue == "Distribución por Área de Prestación" || cellValue == "Área de Reciclaje")
                    {
                        for (int col = 0; col < 9; col++) // Distribution table has 9 columns
                        {
                            var cell = row.GetCell(col);
                            if (cell != null)
                            {
                                cell.CellStyle = headerStyle;
                            }
                        }
                    }
                }
            }
        }
    }

    private void ApplyDataFormatting(ISheet sheet, ICellStyle dataStyle, ICellStyle integerStyle, ICellStyle calculatedStyle, ICellStyle compensationStyle)
    {
        // Apply data formatting to balance sheet data rows (starting from row 7)
        // Row 7 is the first data row, NOT a header row
        // and other data rows throughout the sheet
        for (int rowIndex = 7; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0) ?? row.GetCell(1);
                var cellValue = firstCell?.StringCellValue ?? "";

                // Skip header rows and total rows - only format actual data rows
                if (!cellValue.Contains("TOTAL") && !cellValue.Contains("SUMA") &&
                    !cellValue.Contains("Concepto") && !cellValue.Contains("Área de Reciclaje") &&
                    !cellValue.Contains("Resumen") && !cellValue.Contains("Distribución"))
                {
                    // Apply formatting to all columns for balance sheet data
                    for (int col = 0; col < 12; col++)
                    {
                        var cell = row.GetCell(col);
                        if (cell != null)
                        {
                            // Apply calculated style to formula columns (7, 10, 11) in balance sheet
                            if (col == 7 || col == 10 || col == 11)
                            {
                                cell.CellStyle = calculatedStyle;
                            }
                            // Apply data style to text columns (0, 1) and numeric columns (2-6, 8-9)
                            else if (col >= 2 || (col <= 1 && cell.CellType != CellType.Blank))
                            {
                                cell.CellStyle = dataStyle;
                            }
                        }
                    }

                    // Apply formatting to distribution table data (9 columns)
                    for (int col = 0; col < 9; col++)
                    {
                        var cell = row.GetCell(col);
                        if (cell != null && col < 9)
                        {
                            // Compensación column (column 8) should use compensation formatting for negative values
                            if (col == 8)
                            {
                                cell.CellStyle = compensationStyle;
                            }
                            // Viajes column (column 2) should use integer formatting
                            else if (col == 2)
                            {
                                cell.CellStyle = integerStyle;
                            }
                            else
                            {
                                cell.CellStyle = dataStyle;
                            }
                        }
                    }
                }
            }
        }
    }

    private void ApplyTotalFormatting(ISheet sheet, ICellStyle totalStyle)
    {
        // Apply to total rows - this would need to be identified based on content
        // For now, applying to rows that contain "TOTAL" or "SUMA TOTAL"
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0) ?? row.GetCell(1);
                if (firstCell != null && (firstCell.StringCellValue.Contains("TOTAL") || firstCell.StringCellValue.Contains("SUMA")))
                {
                    for (int col = 0; col < 12; col++)
                    {
                        var cell = row.GetCell(col);
                        if (cell != null)
                        {
                            cell.CellStyle = totalStyle;
                        }
                    }
                }
            }
        }
    }

    private void ApplyDistributionTableBorders(ISheet sheet, IWorkbook workbook)
    {
        // Find the distribution table boundaries
        int distributionStartRow = -1;
        int distributionEndRow = -1;

        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell(0);
                if (firstCell != null)
                {
                    var cellValue = firstCell.StringCellValue;
                    if (cellValue == "Distribución por Área de Prestación")
                    {
                        distributionStartRow = rowIndex;
                    }
                    else if (distributionStartRow != -1 && cellValue == "TOTAL")
                    {
                        distributionEndRow = rowIndex;
                        break;
                    }
                }
            }
        }

        if (distributionStartRow == -1 || distributionEndRow == -1) return;

        // Apply specialized border formatting to distribution table
        for (int rowIndex = distributionStartRow; rowIndex <= distributionEndRow; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                bool isTopRow = rowIndex == distributionStartRow;
                bool isBottomRow = rowIndex == distributionEndRow;

                // Check if this is a header row (title or column headers)
                var firstCell = row.GetCell(0);
                bool isHeaderRow = false;
                if (firstCell != null)
                {
                    var cellValue = firstCell.StringCellValue;
                    isHeaderRow = cellValue == "Distribución por Área de Prestación" || cellValue == "Área de Reciclaje";
                }

                for (int col = 0; col < 9; col++) // Distribution table has 9 columns
                {
                    var cell = row.GetCell(col);
                    if (cell != null)
                    {
                        bool isLeftColumn = col == 0;
                        bool isRightColumn = col == 8;

                        ICellStyle borderStyle;

                        // Header rows and totals row get thick borders on all sides (like row 38)
                        if (isHeaderRow)
                        {
                            // For header rows, preserve the existing header style and only update borders
                            var currentStyle = cell.CellStyle;
                            borderStyle = workbook.CreateCellStyle();
                            borderStyle.CloneStyleFrom(currentStyle);

                            // Apply thick borders on all sides for headers
                            borderStyle.BorderTop = BorderStyle.Thick;
                            borderStyle.BorderBottom = BorderStyle.Thick;
                            borderStyle.BorderLeft = BorderStyle.Thick;
                            borderStyle.BorderRight = BorderStyle.Thick;
                        }
                        else if (isBottomRow)
                        {
                            // For totals row, use specialized styles with thick borders
                            if (col == 2) // Viajes column
                            {
                                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, false, false, true, false, false);
                                // Override to thick borders on all sides for totals
                                borderStyle.BorderTop = BorderStyle.Thick;
                                borderStyle.BorderBottom = BorderStyle.Thick;
                                borderStyle.BorderLeft = BorderStyle.Thick;
                                borderStyle.BorderRight = BorderStyle.Thick;
                            }
                            else if (col == 8) // Compensación column
                            {
                                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, false, false, true, false, false);
                                // Override to thick borders on all sides for totals
                                borderStyle.BorderTop = BorderStyle.Thick;
                                borderStyle.BorderBottom = BorderStyle.Thick;
                                borderStyle.BorderLeft = BorderStyle.Thick;
                                borderStyle.BorderRight = BorderStyle.Thick;
                            }
                            else
                            {
                                borderStyle = CreateDistributionTableBorderStyle(workbook, false, false, true, false, false);
                                // Override to thick borders on all sides for totals
                                borderStyle.BorderTop = BorderStyle.Thick;
                                borderStyle.BorderBottom = BorderStyle.Thick;
                                borderStyle.BorderLeft = BorderStyle.Thick;
                                borderStyle.BorderRight = BorderStyle.Thick;
                            }
                        }
                        else
                        {
                            // Data rows get normal border treatment
                            if (col == 2) // Viajes column
                            {
                                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
                            }
                            else if (col == 8) // Compensación column
                            {
                                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
                            }
                            else
                            {
                                borderStyle = CreateDistributionTableBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
                            }
                        }

                        // Apply the border style
                        if (isHeaderRow)
                        {
                            // For header rows, the borderStyle already preserves all formatting
                            cell.CellStyle = borderStyle;
                        }
                        else
                        {
                            // For data and totals rows, preserve existing formatting but update borders
                            var currentStyle = cell.CellStyle;
                            borderStyle.FillForegroundColor = currentStyle.FillForegroundColor;
                            borderStyle.FillPattern = currentStyle.FillPattern;
                            var font = borderStyle.GetFont(workbook);
                            var currentFont = currentStyle.GetFont(workbook);
                            font.IsBold = currentFont.IsBold;
                            font.Color = currentFont.Color;
                            borderStyle.SetFont(font);

                            cell.CellStyle = borderStyle;
                        }
                    }
                }
            }
        }
    }

    private void ApplyBordersToMergedRegions(ISheet sheet)
    {
        var workbook = sheet.Workbook;

        // Get all merged regions in the sheet
        for (int i = 0; i < sheet.NumMergedRegions; i++)
        {
            var mergedRegion = sheet.GetMergedRegion(i);

            // Apply borders to all cells in the merged region
            for (int row = mergedRegion.FirstRow; row <= mergedRegion.LastRow; row++)
            {
                var sheetRow = sheet.GetRow(row);
                if (sheetRow != null)
                {
                    for (int col = mergedRegion.FirstColumn; col <= mergedRegion.LastColumn; col++)
                    {
                        var cell = sheetRow.GetCell(col);
                        if (cell != null)
                        {
                            var currentStyle = cell.CellStyle;

                            // Create a new style that preserves existing formatting but ensures borders
                            var newStyle = workbook.CreateCellStyle();
                            newStyle.CloneStyleFrom(currentStyle);

                            // Apply borders based on position in merged region
                            bool isTopRow = row == mergedRegion.FirstRow;
                            bool isBottomRow = row == mergedRegion.LastRow;
                            bool isLeftColumn = col == mergedRegion.FirstColumn;
                            bool isRightColumn = col == mergedRegion.LastColumn;

                            // Apply borders to the perimeter of the merged region
                            if (isTopRow && currentStyle.BorderTop != BorderStyle.None)
                            {
                                newStyle.BorderTop = currentStyle.BorderTop;
                                newStyle.TopBorderColor = currentStyle.TopBorderColor;
                            }
                            if (isBottomRow && currentStyle.BorderBottom != BorderStyle.None)
                            {
                                newStyle.BorderBottom = currentStyle.BorderBottom;
                                newStyle.BottomBorderColor = currentStyle.BottomBorderColor;
                            }
                            if (isLeftColumn && currentStyle.BorderLeft != BorderStyle.None)
                            {
                                newStyle.BorderLeft = currentStyle.BorderLeft;
                                newStyle.LeftBorderColor = currentStyle.LeftBorderColor;
                            }
                            if (isRightColumn && currentStyle.BorderRight != BorderStyle.None)
                            {
                                newStyle.BorderRight = currentStyle.BorderRight;
                                newStyle.RightBorderColor = currentStyle.RightBorderColor;
                            }

                            cell.CellStyle = newStyle;
                        }
                    }
                }
            }
        }
    }

    private ExcelFormat GetExcelFormat(string? format)
    {
        if (string.IsNullOrEmpty(format))
        {
            // Try to get from headers
            format = _httpContextAccessor.HttpContext?.Request.Headers["X-ExcelFormat"];
        }

        if (Enum.TryParse<ExcelFormat>(format, ignoreCase: true, out var excelFormat))
        {
            return excelFormat;
        }

        return ExcelFormat.xlsx; // Default to xlsx
    }

    private string GetContentType(ExcelFormat format)
    {
        return format switch
        {
            ExcelFormat.xlsx => ExcelFormatContentType.Xlsx,
            ExcelFormat.xls => ExcelFormatContentType.Xls,
            ExcelFormat.csv => ExcelFormatContentType.Csv,
            _ => ExcelFormatContentType.Xlsx
        };
    }

    private string GenerateFileName(DateTime fromDate, string extension)
    {
        return $"{MassBalanceFilePrefix}{fromDate:yyyyMM}.{extension}";
    }

    // Data retrieval methods (reused from GetMassBalanceHandler)
    private async Task<IEnumerable<Distribution>> GetDistributions(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var distributions = await _unitOfWork
            .DistributionRepository
            .GetAllAsync(isPaginated: false,
                predicate: d => d.Year == request.FromDate.Year
                                && d.Month == request.FromDate.Month,
                useCache: false,
                cancellationToken: cancellationToken);

        if (distributions.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new DistributionsNotFound()));

        return distributions.Results;
    }

    private async Task<IEnumerable<ReportFormat14>> GetReportFormat14(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat14 = await _unitOfWork
            .ReportsFormat14
            .GetAllAsync(isPaginated: false,
                predicate: r => r.VehicleArrival >= DateOnly.FromDateTime(request.FromDate)
                                && r.VehicleArrival < DateOnly.FromDateTime(request.ToDate),
                useCache: false,
                cancellationToken: cancellationToken);

        var tolls = (await _unitOfWork
            .Tolls
            .GetAllAsync(
                isPaginated: false,
                cancellationToken: cancellationToken)).Results;

        var processedReport = ReportFormat14Factory
            .Create(reportFormat14.Results, tolls, DateOnly.FromDateTime(request.FromDate))
            .GetReportFormat14();

        if (!processedReport.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat14NotFound()));

        return processedReport;
    }

    private async Task<IEnumerable<ReportFormat34>> GetReportFormat34(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var reportFormat34 = await _unitOfWork
            .ReportsFormat34
            .GetFilteredReportAsync(
                predicate: r => r.FilteredDate >= DateOnly.FromDateTime(request.FromDate)
                           && r.FilteredDate < DateOnly.FromDateTime(request.ToDate)
                           && r.Tons > 0,
                isPaginated: false,
                sortingOptions: new SortingOptions(SortDirection.Ascending, "FilteredDate"),
                cancellationToken: cancellationToken);

        if (!reportFormat34.Results.Any())
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new ReportFormat34NotFound()));

        return reportFormat34.Results;
    }

    private async Task<IEnumerable<RecyclingArea>> GetRecyclingAreas(CancellationToken cancellationToken)
    {
        var recyclingAreas = await _unitOfWork
            .RecyclingAreaRepository
            .GetAllAsync(isPaginated: false,
                useCache: true,
                cacheExpirationInMinutes: MinutesTillCacheInvalidation,
                cancellationToken: cancellationToken);

        if (recyclingAreas.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new RecyclingAreaNotFound()));

        return recyclingAreas.Results;
    }

    private async Task<IEnumerable<WeighingScale>> GetWeighins(ExportMassBalanceRequest request, CancellationToken cancellationToken)
    {
        var weighins = await _unitOfWork
            .WeighingScales
            .GetAllAsync(includes: w => w.Town,
                isPaginated: false,
                predicate: r => r.EntryDate >= request.FromDate
                                     && r.EntryDate < request.ToDate
                                     && r.CancelDate == null,
                useCache: false,
                cancellationToken: cancellationToken);

        if (weighins.TotalRecords == 0)
            throw new OrionException(
                _unitOfWork.ErrorService.GenerateError(new WeighingScaleNotFound()));

        return weighins.Results;
    }
}
