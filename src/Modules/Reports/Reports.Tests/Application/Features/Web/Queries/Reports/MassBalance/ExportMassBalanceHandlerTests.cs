using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web.Queries.Reports.MassBalance;
using Reports.Domain.Entities;
using Reports.Domain.ValueObjects;

namespace Reports.Tests.Application.Features.Web.Queries.Reports.MassBalance;

/// <summary>
/// Unit tests for ExportMassBalanceHandler validation
/// </summary>
public class ExportMassBalanceHandlerTests
{
    [Fact]
    public void ExportMassBalanceRequest_WithValidData_ShouldPassValidation()
    {
        // Arrange
        var request = new ExportMassBalanceRequest
        {
            FromDate = new DateTime(2025, 3, 1),
            ToDate = new DateTime(2025, 3, 31),
            ExcelFormat = "xlsx"
        };

        var validator = new ExportMassBalanceValidator();

        // Act
        var result = validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void ExportMassBalanceRequest_WithInvalidDateRange_ShouldFailValidation()
    {
        // Arrange
        var request = new ExportMassBalanceRequest
        {
            FromDate = new DateTime(2025, 3, 31),
            ToDate = new DateTime(2025, 3, 1), // ToDate before FromDate
            ExcelFormat = "xlsx"
        };

        var validator = new ExportMassBalanceValidator();

        // Act
        var result = validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("ToDate must be greater than FromDate"));
    }

    [Theory]
    [InlineData("xlsx")]
    [InlineData("xls")]
    [InlineData("csv")]
    [InlineData("")]
    [InlineData(null)]
    public void ExportMassBalanceRequest_WithValidFormats_ShouldPassValidation(string? format)
    {
        // Arrange
        var request = new ExportMassBalanceRequest
        {
            FromDate = new DateTime(2025, 3, 1),
            ToDate = new DateTime(2025, 3, 31),
            ExcelFormat = format
        };

        var validator = new ExportMassBalanceValidator();

        // Act
        var result = validator.Validate(request);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact]
    public void ExportMassBalanceRequest_WithInvalidFormat_ShouldFailValidation()
    {
        // Arrange
        var request = new ExportMassBalanceRequest
        {
            FromDate = new DateTime(2025, 3, 1),
            ToDate = new DateTime(2025, 3, 31),
            ExcelFormat = "invalid"
        };

        var validator = new ExportMassBalanceValidator();

        // Act
        var result = validator.Validate(request);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("ExcelFormat must be one of: xlsx, xls, csv"));
    }

    [Fact]
    public void MassBalanceExportDto_ShouldHaveCorrectStructure()
    {
        // Arrange & Act
        var exportDto = new MassBalanceExportDto
        {
            Title = "Balance de Masas",
            GenerationDate = DateTime.Now,
            Period = "2025/03",
            BalanceSheetRows = new List<BalanceSheetRowDto>(),
            FinalDispositionSummary = new FinalDispositionSummaryDto(),
            DistributionRows = new List<DistributionRowDto>()
        };

        // Assert
        exportDto.Should().NotBeNull();
        exportDto.Title.Should().Be("Balance de Masas");
        exportDto.Period.Should().Be("2025/03");
        exportDto.BalanceSheetRows.Should().NotBeNull();
        exportDto.FinalDispositionSummary.Should().NotBeNull();
        exportDto.DistributionRows.Should().NotBeNull();
    }

    [Fact]
    public void BalanceSheetRowDto_ShouldMapAreaNamesCorrectly()
    {
        // Arrange & Act
        var balanceSheetRow = new BalanceSheetRowDto
        {
            AreaCode = "440405001",
            AreaName = "Medellín",
            UrbanCleaningTons = 125.450m,
            SweepingTons = 89.320m,
            NonRecyclableTons = 234.780m,
            RejectionTons = 12.150m,
            RecyclableTons = 45.670m,
            TotalByNUAP = 350.250m,
            Discounts = 15.750m
        };

        // Assert
        balanceSheetRow.Should().NotBeNull();
        balanceSheetRow.AreaCode.Should().Be("440405001");
        balanceSheetRow.AreaName.Should().Be("Medellín");
        balanceSheetRow.UrbanCleaningTons.Should().Be(125.450m);
        balanceSheetRow.TotalByNUAP.Should().Be(350.250m);
    }

    [Fact]
    public void DistributionRowDto_ShouldIncludeViajesInTotals()
    {
        // Arrange & Act
        var distributionRows = new List<DistributionRowDto>
        {
            new DistributionRowDto
            {
                RecyclingArea = "Medellín",
                ReportedTons = 125.450m,
                Trips = 85, // Integer value for Viajes
                CalculatedDistributedTons = 120.300m,
                CalculatedTotalTons = 125.450m,
                CalculatedDeviationTons = 5.150m,
                TollSharedRouteTons = 15.200m,
                CalculatedDistributionTollPercentage = 0.12m,
                Compensation = 14.436m
            },
            new DistributionRowDto
            {
                RecyclingArea = "Itaguí",
                ReportedTons = 89.320m,
                Trips = 67, // Integer value for Viajes
                CalculatedDistributedTons = 85.100m,
                CalculatedTotalTons = 89.320m,
                CalculatedDeviationTons = 4.220m,
                TollSharedRouteTons = 12.800m,
                CalculatedDistributionTollPercentage = 0.14m,
                Compensation = 11.914m
            }
        };

        // Assert
        distributionRows.Should().HaveCount(2);
        distributionRows.Sum(d => d.Trips).Should().Be(152); // Total Viajes should be calculated
        distributionRows.All(d => d.Trips > 0).Should().BeTrue();
        distributionRows.All(d => d.Trips % 1 == 0).Should().BeTrue(); // Should be integers
    }

    [Fact]
    public void DistributionRowDto_ShouldHandleNegativeCompensation()
    {
        // Arrange & Act
        var distributionRowWithNegativeCompensation = new DistributionRowDto
        {
            RecyclingArea = "Test Area",
            ReportedTons = 100.000m,
            Trips = 50,
            CalculatedDistributedTons = 95.000m,
            CalculatedTotalTons = 100.000m,
            CalculatedDeviationTons = 5.000m,
            TollSharedRouteTons = 20.000m,
            CalculatedDistributionTollPercentage = -0.15m, // Negative percentage
            Compensation = -14.250m // This should be negative: 95.000 * -0.15 = -14.250
        };

        // Assert
        distributionRowWithNegativeCompensation.Should().NotBeNull();
        distributionRowWithNegativeCompensation.CalculatedDistributionTollPercentage.Should().Be(-0.15m);
        distributionRowWithNegativeCompensation.Compensation.Should().Be(-14.250m);
        distributionRowWithNegativeCompensation.Compensation.Should().BeLessThan(0); // Verify it's negative
    }

    [Fact]
    public void MassBalanceExportDto_ShouldHaveConsistentHeaderStructure()
    {
        // Arrange & Act
        var exportDto = new MassBalanceExportDto
        {
            Title = "Balance de Masas",
            GenerationDate = DateTime.Now,
            Period = "2025/03",
            BalanceSheetRows = new List<BalanceSheetRowDto>
            {
                new BalanceSheetRowDto
                {
                    AreaCode = "440405001",
                    AreaName = "Medellín",
                    UrbanCleaningTons = 125.450m,
                    SweepingTons = 89.320m,
                    NonRecyclableTons = 234.780m,
                    RejectionTons = 12.150m,
                    RecyclableTons = 45.670m,
                    TotalByNUAP = 350.250m,
                    Discounts = 15.750m
                }
            },
            FinalDispositionSummary = new FinalDispositionSummaryDto
            {
                WeighingEmvariasTons = 1250.500m,
                WeighingTotalTons = 1350.750m,
                FinalDispositionEmvariasTons = 1245.320m,
                FinalDispositionTotalTons = 1340.890m,
                FinalDispositionDiscountTons = 45.250m
            },
            DistributionRows = new List<DistributionRowDto>
            {
                new DistributionRowDto
                {
                    RecyclingArea = "Medellín",
                    ReportedTons = 125.450m,
                    Trips = 85,
                    CalculatedDistributedTons = 120.300m,
                    CalculatedTotalTons = 125.450m,
                    CalculatedDeviationTons = 5.150m,
                    TollSharedRouteTons = 15.200m,
                    CalculatedDistributionTollPercentage = 0.12m,
                    Compensation = 14.436m
                }
            }
        };

        // Assert - Verify all sections have proper structure for header formatting
        exportDto.Should().NotBeNull();
        exportDto.Title.Should().Be("Balance de Masas");
        exportDto.BalanceSheetRows.Should().HaveCount(1);
        exportDto.FinalDispositionSummary.Should().NotBeNull();
        exportDto.DistributionRows.Should().HaveCount(1);

        // Verify distribution table structure for header consistency
        var distributionRow = exportDto.DistributionRows.First();
        distributionRow.RecyclingArea.Should().Be("Medellín");
        distributionRow.Trips.Should().Be(85);
        distributionRow.Compensation.Should().Be(14.436m);
    }
}
